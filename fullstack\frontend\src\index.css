@import "tailwindcss";

/* Custom scrollbar styles for handicapper grids */
.handicapper-scroll {
  scrollbar-width: thin;
  scrollbar-color: #3b82f6 #1f2937;
  padding-right: 8px; /* Add padding to move content away from scrollbar */
}

.handicapper-scroll::-webkit-scrollbar {
  width: 6px;
}

.handicapper-scroll::-webkit-scrollbar-track {
  background: #1f2937;
  border-radius: 3px;
  margin-right: 2px; /* Add margin to move scrollbar further right */
}

.handicapper-scroll::-webkit-scrollbar-thumb {
  background: #3b82f6;
  border-radius: 3px;
}

.handicapper-scroll::-webkit-scrollbar-thumb:hover {
  background: #60a5fa;
}

/* Custom scrollbar styles for picks dropdown */
.picks-dropdown-scroll {
  scrollbar-width: thin;
  scrollbar-color: #6b7280 #374151;
  padding-right: 8px;
}

.picks-dropdown-scroll::-webkit-scrollbar {
  width: 6px;
}

.picks-dropdown-scroll::-webkit-scrollbar-track {
  background: #374151;
  border-radius: 3px;
  margin-right: 2px;
}

.picks-dropdown-scroll::-webkit-scrollbar-thumb {
  background: #6b7280;
  border-radius: 3px;
}

.picks-dropdown-scroll::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
