import React from "react";
import { HiUser, HiStar } from "react-icons/hi2";
import { IoShirtOutline } from "react-icons/io5";
import { getConfidenceColor } from "../../utils/colorUtils";
import { usePicks } from "../../contexts/PicksContext";

interface Pick {
  id: number;
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
  confidence?: number;
}

interface Handicapper {
  id: number;
  name: string;
  sports: string;
  rating: number;
  accuracy: string;
  profileImage: string;
  picks: Pick[];
}

interface HandicappersViewProps {
  handicappers: Handicapper[];
}

function HandicappersView({ handicappers }: HandicappersViewProps) {
  const { addPick, isPickSelected } = usePicks();

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {handicappers.map((handicapper) => (
        <div
          key={handicapper.id}
          className="bg-[#233e6c] rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 ease-linear hover:cursor-pointer hover:scale-[101.5%]"
        >
          <div className="flex items-center gap-4 mb-6">
            <div className="w-16 h-16 rounded-full bg-gray-700 flex items-center justify-center overflow-hidden">
              <HiUser className="w-10 h-10 text-gray-400" />
            </div>
            <div className="flex-1">
              <h3 className="text-white text-xl font-bold">
                {handicapper.name}
              </h3>
              <p className="text-white text-sm font-bold">
                {handicapper.sports}
              </p>
              <div className="flex items-center gap-2 mt-1">
                <div className="flex">
                  {Array.from({ length: 5 }).map((_, index) => (
                    <HiStar
                      key={index}
                      className={`w-4 h-4 ${
                        index < handicapper.rating
                          ? "text-yellow-400"
                          : "text-gray-600"
                      }`}
                    />
                  ))}
                </div>
                <span className="text-gray-400 text-sm">
                  {handicapper.accuracy} Accuracy
                </span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-3">
            {handicapper.picks.map((pick) => (
              <div
                key={pick.id}
                className="bg-[#061844] rounded-lg p-3 text-center h-48 w-full flex flex-col justify-between hover:bg-[#0a1a52] hover:scale-[102%] hover:shadow-lg transition-all duration-200 ease-linear hover:cursor-pointer"
              >
                <div className="flex flex-col items-center">
                  <div
                    className="w-12 h-12 rounded-full mb-2 flex items-center justify-center bg-gradient-to-b from-gray-800 to-gray-900 relative overflow-hidden"
                    style={{
                      border: `2px solid ${getConfidenceColor(
                        pick.confidence || 75
                      )}`,
                    }}
                  >
                    <IoShirtOutline
                      className="w-8 h-8 absolute"
                      style={{ color: getConfidenceColor(pick.confidence || 75) }}
                    />
                    <div className="text-white font-bold text-xs z-10 relative">
                      {pick.playerNumber}
                    </div>
                  </div>
                  <h4 className="text-white text-xs font-semibold mb-1 line-clamp-2">
                    {pick.playerName}
                  </h4>
                  <p
                    className="text-xs font-medium mb-1"
                    style={{ color: getConfidenceColor(pick.confidence || 75) }}
                  >
                    {pick.betType}
                  </p>
                </div>
                <div className="flex-1 flex items-center justify-center">
                  <p className="text-gray-400 text-[10px] line-clamp-2 text-center">
                    {pick.gameInfo}
                  </p>
                </div>
                <button 
                  onClick={() => {
                    const isSelected = isPickSelected('handicapper', pick.id, handicapper.name);
                    if (!isSelected) {
                      addPick({
                        sourceType: 'handicapper',
                        sourceId: pick.id,
                        playerName: pick.playerName,
                        playerNumber: pick.playerNumber,
                        betType: pick.betType,
                        gameInfo: pick.gameInfo,
                        confidence: pick.confidence || 75,
                        handicapperName: handicapper.name,
                      });
                    }
                  }}
                  className={`text-xs font-bold px-3 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 ease-linear w-full mt-auto ${
                    isPickSelected('handicapper', pick.id, handicapper.name)
                      ? 'bg-green-600 text-white cursor-default'
                      : 'bg-white hover:bg-gray-300 text-[#061844]'
                  }`}
                >
                  {isPickSelected('handicapper', pick.id, handicapper.name) ? 'Added ✓' : 'Add to List'}
                </button>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}

export default HandicappersView;
